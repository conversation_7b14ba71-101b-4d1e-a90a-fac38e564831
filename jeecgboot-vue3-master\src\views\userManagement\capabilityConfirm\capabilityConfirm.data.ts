import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';

export const columns: BasicColumn[] = [
  // {
  //   title: '序号',
  //   dataIndex: 'content'
  // },
  {
    title: '人员名称',
    dataIndex: 'userName'
  },
  {
    title: '计划完成培训考核数量',
    dataIndex: 'planFinishCount'
  },
  {
    title: '实际完成培训考核数量',
    dataIndex: 'realFinishCount'
  },
  {
    title: '完成率',
    dataIndex: 'finishRate'
  },
  // {
  //   title: '状态',
  //   dataIndex: 'status',
  //   customRender: ({ text }) => {
  //     if (text == '0') {
  //       return '正常';
  //     } else if (text == '1') {
  //       return '失效';
  //     } else {
  //       return text;
  //     }
  //   },
  // },
  // {
  //   title: '创建人',
  //   dataIndex: 'creator_dictText'
  // },
  // {
  //   title: '创建时间',
  //   dataIndex: 'createTime'
  // },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '月份',
    field: 'yearMonth',
    component: 'MonthPicker',
    componentProps: {
      valueFormat: 'YYYY-MM',
    },
  },
  // {
  //   label: '计划内容',
  //   field: 'workshopName',
  //   component: 'Input'
  // },
  // {
  //   label: '计划起止时间',
  //   field: 'timeRange',
  //   component: 'RangePicker',
  //   componentProps: {
  //     valueFormat: 'YYYY-MM-DD',
  //     valueType: 'Date',
  //   },
  // },
  // {
  //   label: '检测区间',
  //   field: 'areaName',
  //   component: 'Input'
  // },
  // {
  //   label: '状态',
  //   field: 'status',
  //   component: 'Select',
  //   defaultValue: '0',
  //   componentProps: {
  //     options: [
  //       { label: '正常', value: '0', key: '1' },
  //       { label: '失效', value: '1', key: '2' },
  //     ],
  //   },
  // },
];

export const formSchema: FormSchema[] = [
  // TODO 主键隐藏字段，目前写死为ID
  { label: '', field: 'id', component: 'Input', show: false },
  {
    label: '车间',
    field: 'workshopName',
    rules: [{ required: true }, { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' }],
    component: 'Input',
  },
  {
    label: '洁净级别',
    field: 'cleanLevel',
    rules: [{ required: true }, { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' }],
    component: 'Input',
  },
  {
    label: '检测区域',
    field: 'areaName',
    rules: [{ required: true }, { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' }],
    component: 'Input',
  },
];
