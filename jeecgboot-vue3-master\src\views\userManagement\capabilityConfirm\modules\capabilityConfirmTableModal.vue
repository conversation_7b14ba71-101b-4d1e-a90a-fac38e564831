<template>
    <BasicModal v-bind="$attrs" @register="registerModal" title="人员上岗能力确认记录" okText="确认" @ok="handleSubmit" :width="1400">
        <div class="table-container">
            <div class="table-header">
                <div class="table-actions">
                    <button class="action-btn" @click="printTable">打印</button>
                </div>
            </div>
            <table :id="printId" border="1" cellspacing="0" cellpadding="5"
                style="width: 100%; border-collapse: collapse">
                <thead>
                    <tr>
                        <th style="text-align: center" colspan="6">技术人员简历表</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <th width="15%">姓名</th>
                        <td width="15%">{{ formData.name }}</td>
                        <th width="15%">性别</th>
                        <td width="15%">{{ formData.gender }}</td>
                        <th width="15%">出生年月</th>
                        <td width="25%">{{ formData.birthDate }}</td>
                    </tr>
                    <tr>
                        <th>民族</th>
                        <td>{{ formData.nation }}</td>
                        <th>职称</th>
                        <td>{{ formData.title }}</td>
                        <th>文化程度</th>
                        <td>{{ formData.education }}</td>
                    </tr>
                    <tr>
                        <th>籍贯</th>
                        <td colspan="5">{{ formData.nativePlace }}</td>
                    </tr>
                    <tr>
                        <th>家庭住址</th>
                        <td colspan="5">{{ formData.address }}</td>
                    </tr>
                    <tr>
                        <th>毕业院校</th>
                        <td colspan="2">{{ formData.school }}</td>
                        <th>所学专业</th>
                        <td colspan="2">{{ formData.major }}</td>
                    </tr>
                    <tr>
                        <th>毕业时间</th>
                        <td colspan="2">{{ formData.graduationDate }}</td>
                        <th>联系方式</th>
                        <td colspan="2">{{ formData.contact }}</td>
                    </tr>
                    <tr>
                        <th>工作部门</th>
                        <td colspan="2">{{ formData.department }}</td>
                        <th>工作岗位</th>
                        <td colspan="2">{{ formData.position }}</td>
                    </tr>
                    <tr>
                        <th colspan="6">教育和工作经历</th>
                    </tr>
                    <tr>
                        <th>年月</th>
                        <th colspan="2">学校或工作单位</th>
                        <th colspan="3">所学专业或工作岗位及工作内容</th>
                    </tr>
                    <tr v-for="(item, index) in formData.educationWorkHistory" :key="index">
                        <td>{{ item.date }}</td>
                        <td colspan="2">{{ item.organization }}</td>
                        <td colspan="3">{{ item.description }}</td>
                    </tr>
                    <tr>
                        <th>获得奖励情况</th>
                        <td colspan="5">{{ formData.awards }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </BasicModal>
</template>
<script lang="ts" name="UserJLModal" setup>
import { ref, computed, unref, reactive } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { message } from 'ant-design-vue';
import printJS from 'print-js';
import { buildUUID } from '/@/utils/uuid';
import { defHttp1 } from '/@/utils/http/axios/index1';


const printId = ref('');

// 声明Emits
const emit = defineEmits(['success', 'register']);

// 定义表单数据
const formData = reactive<any>({});

//表单赋值
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    printId.value = buildUUID().toString();



    // defHttp1.get({ url: `/lims/employee/getWorkExperienceAndEducation?userCode=${data.record.username}` }).then((res) => {
    //     console.log("🚀 ~ file: sampleManagement.vue:197 ~ defHttp1.get ~ res:", res)
    //     if (res.code == 200) {
           
    //     } else {
    //         message.error(res.message);
    //     }
    // });

});

// 打印表格
function printTable() {
    printJS({
        type: 'html',
        printable: printId.value,
        scanStyles: false,
    });
}

async function handleSubmit() {
    closeModal();
}
</script>

<style scoped>
.table-container {
    padding: 20px;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.table-title {
    text-align: center;
    font-weight: bold;
    font-size: 18px;
    margin: 0;
}

.table-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    padding: 5px 10px;
    background-color: #1890ff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.action-btn:hover {
    background-color: #40a9ff;
}

.action-btn:disabled {
    background-color: #d9d9d9;
    cursor: not-allowed;
}

table {
    border: 1px solid #ccc;
    width: 100%;
}

th,
td {
    border: 1px solid #ccc;
    text-align: center;
    padding: 8px;
    height: 40px;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
}

tr:hover {
    background-color: #f5f5f5;
}
</style>
